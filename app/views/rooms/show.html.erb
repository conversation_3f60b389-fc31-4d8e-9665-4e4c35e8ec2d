<style>
  .list-group-item {
    cursor: pointer;
    padding: 10px;
    border: 1px solid #ebebeb;
    margin-bottom: 10px;
    border-radius: 10px;
    width: 200px;
  }

  .list-group-item:hover {
    background-color: #eee;
  }

  .info {
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    font-weight: bold;
    position: absolute;
    bottom: 30px;
    left: 14px;
  }

  .rounded-circle {
    border-radius: 50%;
    height: 50px;
    width: 50px;
  }

</style>
<!-- <h1>Rooms#index</h1> -->

<div class="container" style="display: flex;">
  <div style="height: 100vh;border-right: 1px solid #ececec;padding-left: 10px;padding-right: 10px;width: 60px;">
    <div class="info"><%= image_tag "微笑老虎.png", class: "rounded-circle" %></div>
  </div>
  <div style="height: 100vh;border-right: 1px solid #ececec;padding-left: 10px;padding-right: 10px">
    <div style="margin-bottom: 6px;font-weight: bold;margin-top: 10px;">消息列表</div>
    <div class="list-group">
      <div class="list-group-item">群聊1</div>
      <div class="list-group-item">群聊2</div>
      <div class="list-group-item">群聊3</div>
      <div class="list-group-item">群聊3</div>
    </div>
  </div>
  <div style="height: 100vh;padding-left: 10px;padding-right: 10px">
    <div style="margin-bottom: 6px;font-weight: bold;margin-top: 10px;">聊天区</div>
    <div>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grip-vertical h-2.5 w-2.5"><circle cx="9" cy="12" r="1"></circle><circle cx="9" cy="5" r="1"></circle><circle cx="9" cy="19" r="1"></circle><circle cx="15" cy="12" r="1"></circle><circle cx="15" cy="5" r="1"></circle><circle cx="15" cy="19" r="1"></circle></svg>
    </div>
  </div>
</div>