<style>
  .login-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 11em;
  }
</style>
<%= tag.div(flash[:alert], style: "color:red") if flash[:alert] %>
<%= tag.div(flash[:notice], style: "color:green") if flash[:notice] %>
<div class="login-wrapper">
  <div class="card" style="width: 550px;">
    <div class="center" style="margin-bottom: 1em;font-size: 2em;font-weight: bold;">Chatterly</div>
    <%= form_with url: session_path do |form| %>
      <!-- <div class="d-flex justify-content-between">
        <div style="margin-right: 1em"><button type="button" class="i18n-button"><%= image_tag "i18n.svg" %></button></div>
        <div style="flex: 1;">
          <%= form.label :name, class: 'label' do %>
            <%= form.text_field :name, placeholder: 'Name' %>
            <%= image_tag "user.svg", class: "label-icon" %>
          <% end %>
        </div>
      </div> -->
      <div>
        <%= form.label :email_address, class: 'label' do %>
          <%= form.text_field :email_address, placeholder: 'Email address' %>
          <%= image_tag "email.svg", class: "label-icon" %>
        <% end %>
      </div>
      <div>
        <%= form.label :password, class: 'label' do %>
          <%= form.password_field :password, autocomplete: "current-password", placeholder: 'Password', maxlength: 72 %>
          <%= image_tag "password.svg", class: "label-icon" %>
        <% end %>
      </div>
      <div class="center" style="margin-top: 1.5em">
        <%= form.button class: 'icon-button' do %><%= image_tag "login.svg" %><% end %>
      </div>
      <div class="center" style="margin-top: 1.5em">
        <%= link_to "Register", register_path %>
      </div>
    <% end %>
  </div>
</div>

<br>

<%#= link_to "Forgot password?", new_password_path %>
