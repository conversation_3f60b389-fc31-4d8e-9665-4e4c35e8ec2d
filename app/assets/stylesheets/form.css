.label {
  border: 1px solid rgba(123, 123, 123, 0.5);
  border-radius: 1em;
  padding: 0.6em 1.2em;
  width: 100%;
  margin-bottom: 1em;
  transition: box-shadow 0.12s ease;
  display: flex;
  justify-content: space-between;
}

.label:hover {
  box-shadow: 0 0 0 0.18em rgba(123, 123, 123, 0.5);
}

.label:has(input:focus) {
  border: 1px solid rgba(0, 111, 222, 0.4);
  outline: none;
  box-shadow: 0 0 0 0.18em rgba(0, 111, 222, 0.4);
}

.label-icon {
  width: 36px;
  margin-left: 10px;
}

input {
  outline: none;
  border: none;
  width: 100%;
  background-clip: text;
  font-size: 1.4em;
}

input::placeholder {
  color: rgba(123, 123, 123, 0.5);
}

input::selection,
textarea::selection {
  background: hsla(210, 100%, 77%, 0.2);
}