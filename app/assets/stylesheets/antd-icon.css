@font-face {
  font-family: "iconfont"; /* Project id 4912104 */
  src: url('//at.alicdn.com/t/c/font_4912104_k3o72x9nn29.woff2?t=1746339047043') format('woff2'),
       url('//at.alicdn.com/t/c/font_4912104_k3o72x9nn29.woff?t=1746339047043') format('woff'),
       url('//at.alicdn.com/t/c/font_4912104_k3o72x9nn29.ttf?t=1746339047043') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-folder:before {
  content: "\e810";
}

.icon-alert-fill:before {
  content: "\e910";
}

.icon-folder-open:before {
  content: "\e811";
}

.icon-api-fill:before {
  content: "\e911";
}

.icon-folder-add:before {
  content: "\e812";
}

.icon-highlight-fill:before {
  content: "\e912";
}

.icon-deploymentunit:before {
  content: "\e813";
}

.icon-phone-fill:before {
  content: "\e913";
}

.icon-accountbook:before {
  content: "\e814";
}

.icon-edit-fill:before {
  content: "\e914";
}

.icon-contacts:before {
  content: "\e815";
}

.icon-pushpin-fill:before {
  content: "\e915";
}

.icon-carryout:before {
  content: "\e816";
}

.icon-rocket-fill:before {
  content: "\e916";
}

.icon-calendar-check:before {
  content: "\e817";
}

.icon-thunderbolt-fill:before {
  content: "\e917";
}

.icon-calendar:before {
  content: "\e818";
}

.icon-tag-fill:before {
  content: "\e918";
}

.icon-scan:before {
  content: "\e819";
}

.icon-wrench-fill:before {
  content: "\e919";
}

.icon-select:before {
  content: "\e81a";
}

.icon-tags-fill:before {
  content: "\e91a";
}

.icon-boxplot:before {
  content: "\e81b";
}

.icon-bank-fill:before {
  content: "\e91b";
}

.icon-build:before {
  content: "\e81c";
}

.icon-camera-fill:before {
  content: "\e91c";
}

.icon-sliders:before {
  content: "\e81d";
}

.icon-error-fill:before {
  content: "\e91d";
}

.icon-laptop:before {
  content: "\e81e";
}

.icon-crown-fill:before {
  content: "\e91e";
}

.icon-barcode:before {
  content: "\e81f";
}

.icon-mail-fill:before {
  content: "\e91f";
}

.icon-camera:before {
  content: "\e820";
}

.icon-car-fill:before {
  content: "\e920";
}

.icon-cluster:before {
  content: "\e821";
}

.icon-printer-fill:before {
  content: "\e921";
}

.icon-gateway:before {
  content: "\e822";
}

.icon-shop-fill:before {
  content: "\e922";
}

.icon-car:before {
  content: "\e823";
}

.icon-setting-fill:before {
  content: "\e923";
}

.icon-printer:before {
  content: "\e824";
}

.icon-USB-fill:before {
  content: "\e924";
}

.icon-read:before {
  content: "\e825";
}

.icon-golden-fill:before {
  content: "\e925";
}

.icon-cloud-server:before {
  content: "\e826";
}

.icon-build-fill:before {
  content: "\e926";
}

.icon-cloud-upload:before {
  content: "\e827";
}

.icon-boxplot-fill:before {
  content: "\e927";
}

.icon-cloud:before {
  content: "\e828";
}

.icon-sliders-fill:before {
  content: "\e928";
}

.icon-cloud-download:before {
  content: "\e829";
}

.icon-alibaba:before {
  content: "\e929";
}

.icon-cloud-sync:before {
  content: "\e82a";
}

.icon-antdesign:before {
  content: "\e92a";
}

.icon-video:before {
  content: "\e82b";
}

.icon-ant-cloud:before {
  content: "\e92b";
}

.icon-notification:before {
  content: "\e82c";
}

.icon-behance:before {
  content: "\e92c";
}

.icon-sound:before {
  content: "\e82d";
}

.icon-googleplus:before {
  content: "\e92d";
}

.icon-radarchart:before {
  content: "\e82e";
}

.icon-medium:before {
  content: "\e92e";
}

.icon-qrcode:before {
  content: "\e82f";
}

.icon-google:before {
  content: "\e92f";
}

.icon-fund:before {
  content: "\e830";
}

.icon-IE:before {
  content: "\e930";
}

.icon-image:before {
  content: "\e831";
}

.icon-amazon:before {
  content: "\e931";
}

.icon-mail:before {
  content: "\e832";
}

.icon-slack:before {
  content: "\e932";
}

.icon-table:before {
  content: "\e833";
}

.icon-alipay:before {
  content: "\e933";
}

.icon-idcard:before {
  content: "\e834";
}

.icon-taobao:before {
  content: "\e934";
}

.icon-creditcard:before {
  content: "\e835";
}

.icon-zhihu:before {
  content: "\e935";
}

.icon-heart:before {
  content: "\e836";
}

.icon-HTML:before {
  content: "\e936";
}

.icon-block:before {
  content: "\e837";
}

.icon-linkedin:before {
  content: "\e937";
}

.icon-error:before {
  content: "\e838";
}

.icon-yahoo:before {
  content: "\e938";
}

.icon-star:before {
  content: "\e839";
}

.icon-facebook:before {
  content: "\e939";
}

.icon-gold:before {
  content: "\e83a";
}

.icon-skype:before {
  content: "\e93a";
}

.icon-heatmap:before {
  content: "\e83b";
}

.icon-CodeSandbox:before {
  content: "\e93b";
}

.icon-wifi:before {
  content: "\e83c";
}

.icon-chrome:before {
  content: "\e93c";
}

.icon-attachment:before {
  content: "\e83d";
}

.icon-codepen:before {
  content: "\e93d";
}

.icon-edit:before {
  content: "\e83e";
}

.icon-aliwangwang:before {
  content: "\e93e";
}

.icon-key:before {
  content: "\e83f";
}

.icon-apple:before {
  content: "\e93f";
}

.icon-api:before {
  content: "\e840";
}

.icon-android:before {
  content: "\e940";
}

.icon-disconnect:before {
  content: "\e841";
}

.icon-sketch:before {
  content: "\e941";
}

.icon-highlight:before {
  content: "\e842";
}

.icon-Gitlab:before {
  content: "\e942";
}

.icon-monitor:before {
  content: "\e843";
}

.icon-dribbble:before {
  content: "\e943";
}

.icon-link:before {
  content: "\e844";
}

.icon-instagram:before {
  content: "\e944";
}

.icon-man:before {
  content: "\e845";
}

.icon-reddit:before {
  content: "\e945";
}

.icon-percentage:before {
  content: "\e846";
}

.icon-windows:before {
  content: "\e946";
}

.icon-pushpin:before {
  content: "\e847";
}

.icon-yuque:before {
  content: "\e947";
}

.icon-phone:before {
  content: "\e848";
}

.icon-Youtube:before {
  content: "\e948";
}

.icon-shake:before {
  content: "\e849";
}

.icon-Gitlab-fill:before {
  content: "\e949";
}

.icon-tag:before {
  content: "\e84a";
}

.icon-dropbox:before {
  content: "\e94a";
}

.icon-wrench:before {
  content: "\e84b";
}

.icon-dingtalk:before {
  content: "\e94b";
}

.icon-tags:before {
  content: "\e84c";
}

.icon-android-fill:before {
  content: "\e94c";
}

.icon-scissor:before {
  content: "\e84d";
}

.icon-apple-fill:before {
  content: "\e94d";
}

.icon-mr:before {
  content: "\e84e";
}

.icon-HTML-fill:before {
  content: "\e94e";
}

.icon-share:before {
  content: "\e84f";
}

.icon-windows-fill:before {
  content: "\e94f";
}

.icon-branches:before {
  content: "\e850";
}

.icon-QQ:before {
  content: "\e950";
}

.icon-fork:before {
  content: "\e851";
}

.icon-twitter:before {
  content: "\e951";
}

.icon-shrink:before {
  content: "\e852";
}

.icon-skype-fill:before {
  content: "\e952";
}

.icon-arrawsalt:before {
  content: "\e853";
}

.icon-weibo:before {
  content: "\e953";
}

.icon-verticalright:before {
  content: "\e854";
}

.icon-yuque-fill:before {
  content: "\e954";
}

.icon-verticalleft:before {
  content: "\e855";
}

.icon-Youtube-fill:before {
  content: "\e955";
}

.icon-right:before {
  content: "\e856";
}

.icon-yahoo-fill:before {
  content: "\e956";
}

.icon-left:before {
  content: "\e857";
}

.icon-wechat-fill:before {
  content: "\e957";
}

.icon-up:before {
  content: "\e858";
}

.icon-chrome-fill:before {
  content: "\e958";
}

.icon-down:before {
  content: "\e859";
}

.icon-alipay-circle-fill:before {
  content: "\e959";
}

.icon-fullscreen:before {
  content: "\e85a";
}

.icon-aliwangwang-fill:before {
  content: "\e95a";
}

.icon-fullscreen-exit:before {
  content: "\e85b";
}

.icon-behance-circle-fill:before {
  content: "\e95b";
}

.icon-doubleleft:before {
  content: "\e85c";
}

.icon-amazon-circle-fill:before {
  content: "\e95c";
}

.icon-doubleright:before {
  content: "\e85d";
}

.icon-codepen-circle-fill:before {
  content: "\e95d";
}

.icon-arrowright:before {
  content: "\e85e";
}

.icon-CodeSandbox-circle-f:before {
  content: "\e95e";
}

.icon-arrowup:before {
  content: "\e85f";
}

.icon-dropbox-circle-fill:before {
  content: "\e95f";
}

.icon-arrowleft:before {
  content: "\e860";
}

.icon-github-fill:before {
  content: "\e960";
}

.icon-arrowdown:before {
  content: "\e861";
}

.icon-dribbble-circle-fill:before {
  content: "\e961";
}

.icon-upload:before {
  content: "\e862";
}

.icon-googleplus-circle-f:before {
  content: "\e962";
}

.icon-colum-height:before {
  content: "\e863";
}

.icon-medium-circle-fill:before {
  content: "\e963";
}

.icon-vertical-align-botto:before {
  content: "\e864";
}

.icon-QQ-circle-fill:before {
  content: "\e964";
}

.icon-vertical-align-middl:before {
  content: "\e865";
}

.icon-IE-circle-fill:before {
  content: "\e965";
}

.icon-totop:before {
  content: "\e866";
}

.icon-google-circle-fill:before {
  content: "\e966";
}

.icon-vertical-align-top:before {
  content: "\e867";
}

.icon-dingtalk-circle-fill:before {
  content: "\e967";
}

.icon-download:before {
  content: "\e868";
}

.icon-sketch-circle-fill:before {
  content: "\e968";
}

.icon-sort-descending:before {
  content: "\e869";
}

.icon-slack-circle-fill:before {
  content: "\e969";
}

.icon-sort-ascending:before {
  content: "\e86a";
}

.icon-twitter-circle-fill:before {
  content: "\e96a";
}

.icon-fall:before {
  content: "\e86b";
}

.icon-taobao-circle-fill:before {
  content: "\e96b";
}

.icon-swap:before {
  content: "\e86c";
}

.icon-weibo-circle-fill:before {
  content: "\e96c";
}

.icon-stock:before {
  content: "\e86d";
}

.icon-zhihu-circle-fill:before {
  content: "\e96d";
}

.icon-rise:before {
  content: "\e86e";
}

.icon-reddit-circle-fill:before {
  content: "\e96e";
}

.icon-indent:before {
  content: "\e86f";
}

.icon-alipay-square-fill:before {
  content: "\e96f";
}

.icon-outdent:before {
  content: "\e870";
}

.icon-dingtalk-square-fill:before {
  content: "\e970";
}

.icon-menu:before {
  content: "\e871";
}

.icon-CodeSandbox-square-f:before {
  content: "\e971";
}

.icon-unorderedlist:before {
  content: "\e872";
}

.icon-behance-square-fill:before {
  content: "\e972";
}

.icon-orderedlist:before {
  content: "\e873";
}

.icon-amazon-square-fill:before {
  content: "\e973";
}

.icon-align-right:before {
  content: "\e874";
}

.icon-codepen-square-fill:before {
  content: "\e974";
}

.icon-align-center:before {
  content: "\e875";
}

.icon-dribbble-square-fill:before {
  content: "\e975";
}

.icon-align-left:before {
  content: "\e876";
}

.icon-dropbox-square-fill:before {
  content: "\e976";
}

.icon-pic-center:before {
  content: "\e877";
}

.icon-facebook-fill:before {
  content: "\e977";
}

.icon-pic-right:before {
  content: "\e878";
}

.icon-googleplus-square-f:before {
  content: "\e978";
}

.icon-pic-left:before {
  content: "\e879";
}

.icon-google-square-fill:before {
  content: "\e979";
}

.icon-bold:before {
  content: "\e87a";
}

.icon-instagram-fill:before {
  content: "\e97a";
}

.icon-font-colors:before {
  content: "\e87b";
}

.icon-IE-square-fill:before {
  content: "\e97b";
}

.icon-exclaimination:before {
  content: "\e87c";
}

.icon-medium-square-fill:before {
  content: "\e97c";
}

.icon-check-circle:before {
  content: "\e77d";
}

.icon-font-size:before {
  content: "\e87d";
}

.icon-linkedin-fill:before {
  content: "\e97d";
}

.icon-CI:before {
  content: "\e77e";
}

.icon-infomation:before {
  content: "\e87e";
}

.icon-QQ-square-fill:before {
  content: "\e97e";
}

.icon-Dollar:before {
  content: "\e77f";
}

.icon-line-height:before {
  content: "\e87f";
}

.icon-reddit-square-fill:before {
  content: "\e97f";
}

.icon-compass:before {
  content: "\e780";
}

.icon-strikethrough:before {
  content: "\e880";
}

.icon-twitter-square-fill:before {
  content: "\e980";
}

.icon-close-circle:before {
  content: "\e781";
}

.icon-underline:before {
  content: "\e881";
}

.icon-sketch-square-fill:before {
  content: "\e981";
}

.icon-frown:before {
  content: "\e782";
}

.icon-number:before {
  content: "\e882";
}

.icon-slack-square-fill:before {
  content: "\e982";
}

.icon-info-circle:before {
  content: "\e783";
}

.icon-italic:before {
  content: "\e883";
}

.icon-taobao-square-fill:before {
  content: "\e983";
}

.icon-left-circle:before {
  content: "\e784";
}

.icon-code:before {
  content: "\e884";
}

.icon-weibo-square-fill:before {
  content: "\e984";
}

.icon-down-circle:before {
  content: "\e785";
}

.icon-column-width:before {
  content: "\e885";
}

.icon-zhihu-square-fill:before {
  content: "\e985";
}

.icon-EURO:before {
  content: "\e786";
}

.icon-check:before {
  content: "\e886";
}

.icon-zoomout:before {
  content: "\e986";
}

.icon-copyright:before {
  content: "\e787";
}

.icon-ellipsis:before {
  content: "\e887";
}

.icon-apartment:before {
  content: "\e987";
}

.icon-minus-circle:before {
  content: "\e788";
}

.icon-dash:before {
  content: "\e888";
}

.icon-audio:before {
  content: "\e988";
}

.icon-meh:before {
  content: "\e789";
}

.icon-close:before {
  content: "\e889";
}

.icon-audio-fill:before {
  content: "\e989";
}

.icon-plus-circle:before {
  content: "\e78a";
}

.icon-enter:before {
  content: "\e88a";
}

.icon-robot:before {
  content: "\e98a";
}

.icon-play-circle:before {
  content: "\e78b";
}

.icon-line:before {
  content: "\e88b";
}

.icon-zoomin:before {
  content: "\e98b";
}

.icon-question-circle:before {
  content: "\e78c";
}

.icon-minus:before {
  content: "\e88c";
}

.icon-robot-fill:before {
  content: "\e98c";
}

.icon-Pound:before {
  content: "\e78d";
}

.icon-question:before {
  content: "\e88d";
}

.icon-bug-fill:before {
  content: "\e98d";
}

.icon-right-circle:before {
  content: "\e78e";
}

.icon-rollback:before {
  content: "\e88e";
}

.icon-bug:before {
  content: "\e98e";
}

.icon-smile:before {
  content: "\e78f";
}

.icon-small-dash:before {
  content: "\e88f";
}

.icon-audiostatic:before {
  content: "\e98f";
}

.icon-trademark:before {
  content: "\e790";
}

.icon-pause:before {
  content: "\e890";
}

.icon-comment:before {
  content: "\e990";
}

.icon-time-circle:before {
  content: "\e791";
}

.icon-bg-colors:before {
  content: "\e891";
}

.icon-signal-fill:before {
  content: "\e991";
}

.icon-timeout:before {
  content: "\e792";
}

.icon-crown:before {
  content: "\e892";
}

.icon-verified:before {
  content: "\e992";
}

.icon-earth:before {
  content: "\e793";
}

.icon-drag:before {
  content: "\e893";
}

.icon-shortcut-fill:before {
  content: "\e993";
}

.icon-YUAN:before {
  content: "\e794";
}

.icon-desktop:before {
  content: "\e894";
}

.icon-videocameraadd:before {
  content: "\e994";
}

.icon-up-circle:before {
  content: "\e795";
}

.icon-gift:before {
  content: "\e895";
}

.icon-switchuser:before {
  content: "\e995";
}

.icon-warning-circle:before {
  content: "\e796";
}

.icon-stop:before {
  content: "\e896";
}

.icon-whatsapp:before {
  content: "\e996";
}

.icon-sync:before {
  content: "\e797";
}

.icon-fire:before {
  content: "\e897";
}

.icon-appstoreadd:before {
  content: "\e997";
}

.icon-transaction:before {
  content: "\e798";
}

.icon-thunderbolt:before {
  content: "\e898";
}

.icon-caret-down:before {
  content: "\e998";
}

.icon-undo:before {
  content: "\e799";
}

.icon-check-circle-fill:before {
  content: "\e899";
}

.icon-backward:before {
  content: "\e999";
}

.icon-redo:before {
  content: "\e79a";
}

.icon-left-circle-fill:before {
  content: "\e89a";
}

.icon-caret-up:before {
  content: "\e99a";
}

.icon-reload:before {
  content: "\e79b";
}

.icon-down-circle-fill:before {
  content: "\e89b";
}

.icon-caret-right:before {
  content: "\e99b";
}

.icon-reloadtime:before {
  content: "\e79c";
}

.icon-minus-circle-fill:before {
  content: "\e89c";
}

.icon-caret-left:before {
  content: "\e99c";
}

.icon-message:before {
  content: "\e79d";
}

.icon-close-circle-fill:before {
  content: "\e89d";
}

.icon-fast-backward:before {
  content: "\e99d";
}

.icon-dashboard:before {
  content: "\e79e";
}

.icon-info-circle-fill:before {
  content: "\e89e";
}

.icon-forward:before {
  content: "\e99e";
}

.icon-issuesclose:before {
  content: "\e79f";
}

.icon-up-circle-fill:before {
  content: "\e89f";
}

.icon-fast-forward:before {
  content: "\e99f";
}

.icon-poweroff:before {
  content: "\e7a0";
}

.icon-right-circle-fill:before {
  content: "\e8a0";
}

.icon-search:before {
  content: "\e9a0";
}

.icon-logout:before {
  content: "\e7a1";
}

.icon-plus-circle-fill:before {
  content: "\e8a1";
}

.icon-retweet:before {
  content: "\e9a1";
}

.icon-piechart:before {
  content: "\e7a2";
}

.icon-question-circle-fill:before {
  content: "\e8a2";
}

.icon-login:before {
  content: "\e9a2";
}

.icon-setting:before {
  content: "\e7a3";
}

.icon-EURO-circle-fill:before {
  content: "\e8a3";
}

.icon-step-backward:before {
  content: "\e9a3";
}

.icon-eye:before {
  content: "\e7a4";
}

.icon-frown-fill:before {
  content: "\e8a4";
}

.icon-step-forward:before {
  content: "\e9a4";
}

.icon-location:before {
  content: "\e7a5";
}

.icon-copyright-circle-fil:before {
  content: "\e8a5";
}

.icon-swap-right:before {
  content: "\e9a5";
}

.icon-edit-square:before {
  content: "\e7a6";
}

.icon-CI-circle-fill:before {
  content: "\e8a6";
}

.icon-swap-left:before {
  content: "\e9a6";
}

.icon-export:before {
  content: "\e7a7";
}

.icon-compass-fill:before {
  content: "\e8a7";
}

.icon-woman:before {
  content: "\e9a7";
}

.icon-save:before {
  content: "\e7a8";
}

.icon-Dollar-circle-fill:before {
  content: "\e8a8";
}

.icon-plus:before {
  content: "\e9a8";
}

.icon-Import:before {
  content: "\e7a9";
}

.icon-poweroff-circle-fill:before {
  content: "\e8a9";
}

.icon-eyeclose-fill:before {
  content: "\e9a9";
}

.icon-appstore:before {
  content: "\e7aa";
}

.icon-meh-fill:before {
  content: "\e8aa";
}

.icon-eye-close:before {
  content: "\e9aa";
}

.icon-close-square:before {
  content: "\e7ab";
}

.icon-play-circle-fill:before {
  content: "\e8ab";
}

.icon-clear:before {
  content: "\e9ab";
}

.icon-down-square:before {
  content: "\e7ac";
}

.icon-Pound-circle-fill:before {
  content: "\e8ac";
}

.icon-collapse:before {
  content: "\e9ac";
}

.icon-layout:before {
  content: "\e7ad";
}

.icon-smile-fill:before {
  content: "\e8ad";
}

.icon-expand:before {
  content: "\e9ad";
}

.icon-left-square:before {
  content: "\e7ae";
}

.icon-stop-fill:before {
  content: "\e8ae";
}

.icon-deletecolumn:before {
  content: "\e9ae";
}

.icon-play-square:before {
  content: "\e7af";
}

.icon-warning-circle-fill:before {
  content: "\e8af";
}

.icon-merge-cells:before {
  content: "\e9af";
}

.icon-control:before {
  content: "\e7b0";
}

.icon-time-circle-fill:before {
  content: "\e8b0";
}

.icon-subnode:before {
  content: "\e9b0";
}

.icon-codelibrary:before {
  content: "\e7b1";
}

.icon-trademark-circle-fil:before {
  content: "\e8b1";
}

.icon-rotate-left:before {
  content: "\e9b1";
}

.icon-detail:before {
  content: "\e7b2";
}

.icon-YUAN-circle-fill:before {
  content: "\e8b2";
}

.icon-rotate-right:before {
  content: "\e9b2";
}

.icon-minus-square:before {
  content: "\e7b3";
}

.icon-heart-fill:before {
  content: "\e8b3";
}

.icon-insertrowbelow:before {
  content: "\e9b3";
}

.icon-plus-square:before {
  content: "\e7b4";
}

.icon-piechart-circle-fil:before {
  content: "\e8b4";
}

.icon-insertrowabove:before {
  content: "\e9b4";
}

.icon-right-square:before {
  content: "\e7b5";
}

.icon-dashboard-fill:before {
  content: "\e8b5";
}

.icon-table1:before {
  content: "\e9b5";
}

.icon-project:before {
  content: "\e7b6";
}

.icon-message-fill:before {
  content: "\e8b6";
}

.icon-solit-cells:before {
  content: "\e9b6";
}

.icon-wallet:before {
  content: "\e7b7";
}

.icon-check-square-fill:before {
  content: "\e8b7";
}

.icon-formatpainter:before {
  content: "\e9b7";
}

.icon-up-square:before {
  content: "\e7b8";
}

.icon-down-square-fill:before {
  content: "\e8b8";
}

.icon-insertrowright:before {
  content: "\e9b8";
}

.icon-calculator:before {
  content: "\e7b9";
}

.icon-minus-square-fill:before {
  content: "\e8b9";
}

.icon-formatpainter-fill:before {
  content: "\e9b9";
}

.icon-interation:before {
  content: "\e7ba";
}

.icon-close-square-fill:before {
  content: "\e8ba";
}

.icon-insertrowleft:before {
  content: "\e9ba";
}

.icon-check-square:before {
  content: "\e7bb";
}

.icon-codelibrary-fill:before {
  content: "\e8bb";
}

.icon-translate:before {
  content: "\e9bb";
}

.icon-border:before {
  content: "\e7bc";
}

.icon-left-square-fill:before {
  content: "\e8bc";
}

.icon-deleterow:before {
  content: "\e9bc";
}

.icon-border-outer:before {
  content: "\e7bd";
}

.icon-play-square-fill:before {
  content: "\e8bd";
}

.icon-sisternode:before {
  content: "\e9bd";
}

.icon-border-top:before {
  content: "\e7be";
}

.icon-up-square-fill:before {
  content: "\e8be";
}

.icon-Field-number:before {
  content: "\e9be";
}

.icon-border-bottom:before {
  content: "\e7bf";
}

.icon-right-square-fill:before {
  content: "\e8bf";
}

.icon-Field-String:before {
  content: "\e9bf";
}

.icon-border-left:before {
  content: "\e7c0";
}

.icon-plus-square-fill:before {
  content: "\e8c0";
}

.icon-Function:before {
  content: "\e9c0";
}

.icon-border-right:before {
  content: "\e7c1";
}

.icon-accountbook-fill:before {
  content: "\e8c1";
}

.icon-Field-time:before {
  content: "\e9c1";
}

.icon-border-inner:before {
  content: "\e7c2";
}

.icon-carryout-fill:before {
  content: "\e8c2";
}

.icon-GIF:before {
  content: "\e9c2";
}

.icon-border-verticle:before {
  content: "\e7c3";
}

.icon-calendar-fill:before {
  content: "\e8c3";
}

.icon-Partition:before {
  content: "\e9c3";
}

.icon-border-horizontal:before {
  content: "\e7c4";
}

.icon-calculator-fill:before {
  content: "\e8c4";
}

.icon-index:before {
  content: "\e9c4";
}

.icon-radius-bottomleft:before {
  content: "\e7c5";
}

.icon-interation-fill:before {
  content: "\e8c5";
}

.icon-Storedprocedure:before {
  content: "\e9c5";
}

.icon-radius-bottomright:before {
  content: "\e7c6";
}

.icon-project-fill:before {
  content: "\e8c6";
}

.icon-Field-Binary:before {
  content: "\e9c6";
}

.icon-radius-upleft:before {
  content: "\e7c7";
}

.icon-detail-fill:before {
  content: "\e8c7";
}

.icon-Console-SQL:before {
  content: "\e9c7";
}

.icon-radius-upright:before {
  content: "\e7c8";
}

.icon-save-fill:before {
  content: "\e8c8";
}

.icon-icon-test:before {
  content: "\e9c8";
}

.icon-radius-setting:before {
  content: "\e7c9";
}

.icon-wallet-fill:before {
  content: "\e8c9";
}

.icon-aim:before {
  content: "\e9c9";
}

.icon-adduser:before {
  content: "\e7ca";
}

.icon-control-fill:before {
  content: "\e8ca";
}

.icon-compress:before {
  content: "\e9ca";
}

.icon-deleteteam:before {
  content: "\e7cb";
}

.icon-layout-fill:before {
  content: "\e8cb";
}

.icon-expend:before {
  content: "\e9cb";
}

.icon-deleteuser:before {
  content: "\e7cc";
}

.icon-appstore-fill:before {
  content: "\e8cc";
}

.icon-folder-view:before {
  content: "\e9cc";
}

.icon-addteam:before {
  content: "\e7cd";
}

.icon-mobile-fill:before {
  content: "\e8cd";
}

.icon-file-GIF:before {
  content: "\e9cd";
}

.icon-user:before {
  content: "\e7ce";
}

.icon-tablet-fill:before {
  content: "\e8ce";
}

.icon-group:before {
  content: "\e9ce";
}

.icon-team:before {
  content: "\e7cf";
}

.icon-book-fill:before {
  content: "\e8cf";
}

.icon-send:before {
  content: "\e9cf";
}

.icon-areachart:before {
  content: "\e7d0";
}

.icon-redenvelope-fill:before {
  content: "\e8d0";
}

.icon-Report:before {
  content: "\e9d0";
}

.icon-linechart:before {
  content: "\e7d1";
}

.icon-safetycertificate-f:before {
  content: "\e8d1";
}

.icon-View:before {
  content: "\e9d1";
}

.icon-barchart:before {
  content: "\e7d2";
}

.icon-propertysafety-fill:before {
  content: "\e8d2";
}

.icon-shortcut:before {
  content: "\e9d2";
}

.icon-pointmap:before {
  content: "\e7d3";
}

.icon-insurance-fill:before {
  content: "\e8d3";
}

.icon-ungroup:before {
  content: "\e9d3";
}

.icon-container:before {
  content: "\e7d4";
}

.icon-securityscan-fill:before {
  content: "\e8d4";
}

.icon-database:before {
  content: "\e7d5";
}

.icon-file-exclamation-fil:before {
  content: "\e8d5";
}

.icon-sever:before {
  content: "\e7d6";
}

.icon-file-add-fill:before {
  content: "\e8d6";
}

.icon-mobile:before {
  content: "\e7d7";
}

.icon-file-fill:before {
  content: "\e8d7";
}

.icon-tablet:before {
  content: "\e7d8";
}

.icon-file-excel-fill:before {
  content: "\e8d8";
}

.icon-redenvelope:before {
  content: "\e7d9";
}

.icon-file-markdown-fill:before {
  content: "\e8d9";
}

.icon-book:before {
  content: "\e7da";
}

.icon-file-text-fill:before {
  content: "\e8da";
}

.icon-filedone:before {
  content: "\e7db";
}

.icon-file-ppt-fill:before {
  content: "\e8db";
}

.icon-reconciliation:before {
  content: "\e7dc";
}

.icon-file-unknown-fill:before {
  content: "\e8dc";
}

.icon-file-exception:before {
  content: "\e7dd";
}

.icon-file-word-fill:before {
  content: "\e8dd";
}

.icon-filesync:before {
  content: "\e7de";
}

.icon-file-zip-fill:before {
  content: "\e8de";
}

.icon-filesearch:before {
  content: "\e7df";
}

.icon-file-pdf-fill:before {
  content: "\e8df";
}

.icon-solution:before {
  content: "\e7e0";
}

.icon-file-image-fill:before {
  content: "\e8e0";
}

.icon-fileprotect:before {
  content: "\e7e1";
}

.icon-diff-fill:before {
  content: "\e8e1";
}

.icon-file-add:before {
  content: "\e7e2";
}

.icon-file-copy-fill:before {
  content: "\e8e2";
}

.icon-file-excel:before {
  content: "\e7e3";
}

.icon-snippets-fill:before {
  content: "\e8e3";
}

.icon-file-exclamation:before {
  content: "\e7e4";
}

.icon-batchfolding-fill:before {
  content: "\e8e4";
}

.icon-file-pdf:before {
  content: "\e7e5";
}

.icon-reconciliation-fill:before {
  content: "\e8e5";
}

.icon-file-image:before {
  content: "\e7e6";
}

.icon-folder-add-fill:before {
  content: "\e8e6";
}

.icon-file-markdown:before {
  content: "\e7e7";
}

.icon-folder-fill:before {
  content: "\e8e7";
}

.icon-file-unknown:before {
  content: "\e7e8";
}

.icon-folder-open-fill:before {
  content: "\e8e8";
}

.icon-file-ppt:before {
  content: "\e7e9";
}

.icon-database-fill:before {
  content: "\e8e9";
}

.icon-file-word:before {
  content: "\e7ea";
}

.icon-container-fill:before {
  content: "\e8ea";
}

.icon-file:before {
  content: "\e7eb";
}

.icon-sever-fill:before {
  content: "\e8eb";
}

.icon-file-zip:before {
  content: "\e7ec";
}

.icon-calendar-check-fill:before {
  content: "\e8ec";
}

.icon-file-text:before {
  content: "\e7ed";
}

.icon-image-fill:before {
  content: "\e8ed";
}

.icon-file-copy:before {
  content: "\e7ee";
}

.icon-idcard-fill:before {
  content: "\e8ee";
}

.icon-snippets:before {
  content: "\e7ef";
}

.icon-creditcard-fill:before {
  content: "\e8ef";
}

.icon-audit:before {
  content: "\e7f0";
}

.icon-fund-fill:before {
  content: "\e8f0";
}

.icon-diff:before {
  content: "\e7f1";
}

.icon-read-fill:before {
  content: "\e8f1";
}

.icon-Batchfolding:before {
  content: "\e7f2";
}

.icon-contacts-fill:before {
  content: "\e8f2";
}

.icon-securityscan:before {
  content: "\e7f3";
}

.icon-delete-fill:before {
  content: "\e8f3";
}

.icon-propertysafety:before {
  content: "\e7f4";
}

.icon-notification-fill:before {
  content: "\e8f4";
}

.icon-safetycertificate:before {
  content: "\e7f5";
}

.icon-flag-fill:before {
  content: "\e8f5";
}

.icon-insurance:before {
  content: "\e7f6";
}

.icon-moneycollect-fill:before {
  content: "\e8f6";
}

.icon-alert:before {
  content: "\e7f7";
}

.icon-medicinebox-fill:before {
  content: "\e8f7";
}

.icon-delete:before {
  content: "\e7f8";
}

.icon-rest-fill:before {
  content: "\e8f8";
}

.icon-hourglass:before {
  content: "\e7f9";
}

.icon-shopping-fill:before {
  content: "\e8f9";
}

.icon-bulb:before {
  content: "\e7fa";
}

.icon-skin-fill:before {
  content: "\e8fa";
}

.icon-experiment:before {
  content: "\e7fb";
}

.icon-video-fill:before {
  content: "\e8fb";
}

.icon-bell:before {
  content: "\e7fc";
}

.icon-sound-fill:before {
  content: "\e8fc";
}

.icon-trophy:before {
  content: "\e7fd";
}

.icon-bulb-fill:before {
  content: "\e8fd";
}

.icon-rest:before {
  content: "\e7fe";
}

.icon-bell-fill:before {
  content: "\e8fe";
}

.icon-USB:before {
  content: "\e7ff";
}

.icon-filter-fill:before {
  content: "\e8ff";
}

.icon-skin:before {
  content: "\e800";
}

.icon-fire-fill:before {
  content: "\e900";
}

.icon-home:before {
  content: "\e801";
}

.icon-funnelplot-fill:before {
  content: "\e901";
}

.icon-bank:before {
  content: "\e802";
}

.icon-gift-fill:before {
  content: "\e902";
}

.icon-filter:before {
  content: "\e803";
}

.icon-hourglass-fill:before {
  content: "\e903";
}

.icon-funnelplot:before {
  content: "\e804";
}

.icon-home-fill:before {
  content: "\e904";
}

.icon-like:before {
  content: "\e805";
}

.icon-trophy-fill:before {
  content: "\e905";
}

.icon-unlike:before {
  content: "\e806";
}

.icon-location-fill:before {
  content: "\e906";
}

.icon-unlock:before {
  content: "\e807";
}

.icon-cloud-fill:before {
  content: "\e907";
}

.icon-lock:before {
  content: "\e808";
}

.icon-customerservice-fill:before {
  content: "\e908";
}

.icon-customerservice:before {
  content: "\e809";
}

.icon-experiment-fill:before {
  content: "\e909";
}

.icon-flag:before {
  content: "\e80a";
}

.icon-eye-fill:before {
  content: "\e90a";
}

.icon-moneycollect:before {
  content: "\e80b";
}

.icon-like-fill:before {
  content: "\e90b";
}

.icon-medicinebox:before {
  content: "\e80c";
}

.icon-lock-fill:before {
  content: "\e90c";
}

.icon-shop:before {
  content: "\e80d";
}

.icon-unlike-fill:before {
  content: "\e90d";
}

.icon-rocket:before {
  content: "\e80e";
}

.icon-star-fill:before {
  content: "\e90e";
}

.icon-shopping:before {
  content: "\e80f";
}

.icon-unlock-fill:before {
  content: "\e90f";
}
