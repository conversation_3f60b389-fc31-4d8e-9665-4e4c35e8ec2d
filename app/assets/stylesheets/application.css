/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

.search-box {
  background-color: #ffffff;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.search-box:hover {
  background-color: #c2ccd5;
}

.add-box {
  background-color: #ffffff;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.add-box:hover {
  background-color: #c2ccd5;
}

.room-list {
  padding: 10px;
}

.room-item-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-bottom: 10px;
}

.room-item {
  cursor: pointer;
  background-color: #ffffff;
  width: 280px;
  height: 60px;
  border-radius: 10px;
  transition: all 0.1s ease;
  padding: 10px;
  margin-bottom: 2px;
}

.room-item:hover {
  background-color: #f3f3f3;
}

.room-item.active {
  background-color: #e4edfc;
}

.latest-message {
  font-size: 12px;
  color: #999;
}

.message-header {
  padding: 10px;
  border-bottom: 1px solid #dbdbdb;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-header-title {
  font-size: 18px;
}

.message-header-actions {
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}
