GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.1)
      actionpack (= 8.0.1)
      activesupport (= 8.0.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.1)
      actionpack (= 8.0.1)
      activejob (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      mail (>= 2.8.0)
    actionmailer (8.0.1)
      actionpack (= 8.0.1)
      actionview (= 8.0.1)
      activejob (= 8.0.1)
      activesupport (= 8.0.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.1)
      actionview (= 8.0.1)
      activesupport (= 8.0.1)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.1)
      actionpack (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.1)
      activesupport (= 8.0.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.1)
      activesupport (= 8.0.1)
      globalid (>= 0.3.6)
    activemodel (8.0.1)
      activesupport (= 8.0.1)
    activerecord (8.0.1)
      activemodel (= 8.0.1)
      activesupport (= 8.0.1)
      timeout (>= 0.4.0)
    activestorage (8.0.1)
      actionpack (= 8.0.1)
      activejob (= 8.0.1)
      activerecord (= 8.0.1)
      activesupport (= 8.0.1)
      marcel (~> 1.0)
    activesupport (8.0.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    bcrypt_pbkdf (1.1.1-arm64-darwin)
    bcrypt_pbkdf (1.1.1-x86_64-darwin)
    benchmark (0.4.0)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (6.2.2)
      racc
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    crass (1.0.6)
    date (3.4.1)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    dotenv (3.1.6)
    drb (2.2.1)
    ed25519 (1.3.0)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.0.3)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.14.2)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.9.0)
    kamal (2.4.0)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.2)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    language_server-protocol (3.17.0.3)
    logger (1.6.3)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mini_mime (1.1.5)
    minitest (5.25.4)
    msgpack (1.7.5)
    mysql2 (0.5.6)
    net-imap (0.5.1)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.17.2-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.17.2-arm-linux)
      racc (~> 1.4)
    nokogiri (1.17.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.17.2-x86-linux)
      racc (~> 1.4)
    nokogiri (1.17.2-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.17.2-x86_64-linux)
      racc (~> 1.4)
    ostruct (0.6.1)
    parallel (1.26.3)
    parser (3.3.6.0)
      ast (~> 2.4.1)
      racc
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.1)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.5.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.8)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.1)
      actioncable (= 8.0.1)
      actionmailbox (= 8.0.1)
      actionmailer (= 8.0.1)
      actionpack (= 8.0.1)
      actiontext (= 8.0.1)
      actionview (= 8.0.1)
      activejob (= 8.0.1)
      activemodel (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      bundler (>= 1.15.0)
      railties (= 8.0.1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.1)
      actionpack (= 8.0.1)
      activesupport (= 8.0.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.9.0)
      psych (>= 4.0.0)
    regexp_parser (2.9.3)
    reline (0.5.12)
      io-console (~> 0.5)
    rexml (3.3.9)
    rubocop (1.69.2)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.36.2, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.37.0)
      parser (>= 3.3.1.0)
    rubocop-minitest (0.36.0)
      rubocop (>= 1.61, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-performance (1.23.0)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.27.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails-omakase (1.0.0)
      rubocop
      rubocop-minitest
      rubocop-performance
      rubocop-rails
    ruby-progressbar (1.13.0)
    rubyzip (2.3.2)
    securerandom (0.4.0)
    selenium-webdriver (4.27.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    solid_cable (3.0.4)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.6)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.1.0)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sshkit (1.23.2)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.2)
    thor (1.3.2)
    thruster (0.1.9)
    thruster (0.1.9-aarch64-linux)
    thruster (0.1.9-arm64-darwin)
    thruster (0.1.9-x86_64-darwin)
    thruster (0.1.9-x86_64-linux)
    timeout (0.4.2)
    turbo-rails (2.0.11)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.2)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.2)
    useragent (0.16.11)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket (1.2.11)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.1)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  bcrypt (~> 3.1.7)
  bootsnap
  brakeman
  capybara
  debug
  importmap-rails
  jbuilder
  kamal
  mysql2 (~> 0.5)
  propshaft
  puma (>= 5.0)
  rails (~> 8.0.1)
  rubocop-rails-omakase
  selenium-webdriver
  solid_cable
  solid_cache
  solid_queue
  stimulus-rails
  thruster
  turbo-rails
  tzinfo-data
  web-console

BUNDLED WITH
   2.5.23
